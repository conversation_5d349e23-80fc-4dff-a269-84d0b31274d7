<template>
  <div class="home-container">
    <div class="header">
      <h1>欢迎来到安全系统</h1>
      <div class="user-info">
        <img :src="currentAvatar" class="current-avatar" alt="当前头像" />
        <span v-if="user">{{ user.username }}!</span>
        <button @click="logout" class="logout-btn">退出登录</button>
      </div>
    </div>
    <div v-if="user" class="user-details">
      <p><strong>用户名:</strong> {{ user.username }}</p>
      <p><strong>邮箱:</strong> {{ user.email }}</p>
      <p><strong>用户ID:</strong> {{ user.id }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { authAPI, authUtils } from '../utils/api';

const router = useRouter();
const user = ref(null);

// 检查登录状态
const checkAuth = () => {
  if (!authUtils.isAuthenticated()) {
    router.push('/');
    return false;
  }

  user.value = authUtils.getCurrentUser();
  return true;
};

// 验证token有效性
const verifyToken = async () => {
  try {
    await authAPI.verifyToken();
  } catch (error) {
    console.error('Token验证失败:', error);
    logout();
  }
};

// 退出登录
const logout = () => {
  authUtils.clearAuth();
  router.push('/');
};

onMounted(() => {
  if (checkAuth()) {
    verifyToken();
  }
});
</script>

<style scoped>
.header {
  width: 100vw;
  display: flex;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px 30px;
  border-radius: 15px;
  margin-bottom: 30px;
  color: white;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}
</style>
