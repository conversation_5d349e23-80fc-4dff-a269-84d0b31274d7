import axios from 'axios'
import router from '../router'

// 创建axios实例
const api = axios.create({
  baseURL: 'http://127.0.0.1:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器 - 自动添加token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器 - 统一处理错误
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // Token过期或无效，清除本地存储并跳转到登录页
          localStorage.removeItem('authToken')
          localStorage.removeItem('user')
          if (router.currentRoute.value.path !== '/') {
            router.push('/')
          }
          break
        case 403:
          console.error('权限不足:', data.error)
          break
        case 404:
          console.error('资源不存在:', data.error)
          break
        case 500:
          console.error('服务器错误:', data.error)
          break
        default:
          console.error('请求错误:', data.error || '未知错误')
      }
    } else if (error.request) {
      console.error('网络错误: 无法连接到服务器')
    } else {
      console.error('请求配置错误:', error.message)
    }
    
    return Promise.reject(error)
  }
)

// 认证相关API
export const authAPI = {
  // 登录
  login: (credentials) => api.post('/login', credentials),
  
  // 注册
  register: (userData) => api.post('/register', userData),
  
  // 验证token
  verifyToken: () => api.post('/verify-token'),
  
  // 获取用户信息
  getUserProfile: () => api.get('/user/profile')
}

// 工具函数
export const authUtils = {
  // 检查是否已登录
  isAuthenticated: () => {
    const token = localStorage.getItem('authToken')
    const user = localStorage.getItem('user')
    return !!(token && user)
  },
  
  // 获取当前用户信息
  getCurrentUser: () => {
    const userData = localStorage.getItem('user')
    try {
      return userData ? JSON.parse(userData) : null
    } catch (error) {
      console.error('解析用户数据失败:', error)
      return null
    }
  },
  
  // 设置认证信息
  setAuth: (token, user) => {
    localStorage.setItem('authToken', token)
    localStorage.setItem('user', JSON.stringify(user))
  },
  
  // 清除认证信息
  clearAuth: () => {
    localStorage.removeItem('authToken')
    localStorage.removeItem('user')
  },
  
  // 获取token
  getToken: () => {
    return localStorage.getItem('authToken')
  }
}

export default api
