<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { authAPI, authUtils } from '../src/utils/api';

const router = useRouter();
const user = ref(null);

// 检查登录状态
const checkAuth = () => {
  if (!authUtils.isAuthenticated()) {
    router.push('/');
    return false;
  }

  user.value = authUtils.getCurrentUser();
  return true;
};

// 验证token有效性
const verifyToken = async () => {
  try {
    await authAPI.verifyToken();
  } catch (error) {
    console.error('Token验证失败:', error);
    logout();
  }
};

// 退出登录
const logout = () => {
  authUtils.clearAuth();
  router.push('/');
};

onMounted(() => {
  if (checkAuth()) {
    verifyToken();
  }
});
</script>

<template>
  <div id="app">
        <div class="header">
      <h1>欢迎来到安全系统</h1>
      <div class="user-info">
        <span v-if="user">{{ user.username }}</span>
        <button @click="logout"  v-if="user" class="logout-btn">退出登录</button>
      </div>
    </div>
    <router-view />
  </div>
</template>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 主应用容器 - 上下结构 */
#app {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 头部样式 */
.header {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px 30px;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  flex-shrink: 0; /* 防止头部被压缩 */
}

.header h1 {
  font-size: 1.5rem;
  font-weight: 600;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-info span {
  font-size: 1rem;
  opacity: 0.9;
}

/* 退出按钮样式 */
.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 路由视图容器 - 占据剩余空间 */
.router-view {
  flex: 1;
  width: 100%;
  overflow: auto; /* 允许内容滚动 */
}
</style>
